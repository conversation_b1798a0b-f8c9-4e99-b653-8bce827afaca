[{"key": "tophypo-test", "assigned_fields": [{"field_definition_key": "hdf_property_cubature", "field_definition_flavour": null, "field_definition_return_type": "volume", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "cubature", "priority_mappings": [{"document_category_key": "PROPERTY_INSURANCE", "priority": 10}, {"document_category_key": "PROPERTY_VALUATION_GOV", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_pension2_assets_current", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "current_assets", "priority_mappings": [{"document_category_key": "PENSION_CERTIFICATE", "priority": 10}]}, {"page_object_key": "total_amount", "priority_mappings": [{"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_ahv_n13", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "ahv_new", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 20}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 30}, {"document_category_key": "PAYSLIP", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_date_of_birth", "field_definition_flavour": null, "field_definition_return_type": "date", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "p1_date_of_birth", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "p2_date_of_birth", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "date_of_birth", "priority_mappings": [{"document_category_key": "PASSPORT_CH", "priority": 20}, {"document_category_key": "ID", "priority": 30}, {"document_category_key": "RESIDENCE_PERMIT", "priority": 40}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 50}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 60}, {"document_category_key": "PAYSLIP", "priority": 70}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_cadaster_no", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "cadaster_no", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}, {"document_category_key": "PROPERTY_INSURANCE", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION_GOV", "priority": 30}, {"document_category_key": "PROPERTY_VALUATION", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_egrid", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "land_register_egrid", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_value_ratio", "field_definition_flavour": null, "field_definition_return_type": "value_ratio", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_value_ratio", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}, {"document_category_key": "PROPERTY_VALUATION_GOV", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_year_construction", "field_definition_flavour": null, "field_definition_return_type": "integer", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "year_construction", "priority_mappings": [{"document_category_key": "PROPERTY_INSURANCE", "priority": 10}, {"document_category_key": "PROPERTY_VALUATION_GOV", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}]}, {"key": "hd_internal", "assigned_fields": [{"field_definition_key": "hdf_property_cadaster_no", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "cadaster_no", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}, {"document_category_key": "PROPERTY_INSURANCE", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION_GOV", "priority": 30}, {"document_category_key": "PROPERTY_VALUATION", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_egrid", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "land_register_egrid", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_cubature", "field_definition_flavour": null, "field_definition_return_type": "volume", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "cubature", "priority_mappings": [{"document_category_key": "PROPERTY_INSURANCE", "priority": 10}, {"document_category_key": "PROPERTY_VALUATION_GOV", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_lastname", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "p1_lastname", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "p1_fullname", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}]}, {"page_object_key": "lastname", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 30}, {"document_category_key": "DEBT_COLLECTION_INFORMATION", "priority": 40}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 70}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 80}, {"document_category_key": "PASSPORT_CH", "priority": 90}, {"document_category_key": "ID", "priority": 100}, {"document_category_key": "RESIDENCE_PERMIT", "priority": 110}, {"document_category_key": "PAYSLIP", "priority": 120}]}, {"page_object_key": "fullname", "priority_mappings": [{"document_category_key": "DEBT_COLLECTION_INFORMATION", "priority": 50}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 130}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 140}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 150}, {"document_category_key": "PASSPORT_CH", "priority": 160}, {"document_category_key": "ID", "priority": 170}, {"document_category_key": "RESIDENCE_PERMIT", "priority": 180}, {"document_category_key": "PAYSLIP", "priority": 190}]}, {"page_object_key": "addressline_fullname", "priority_mappings": [{"document_category_key": "DEBT_COLLECTION_INFORMATION", "priority": 60}]}, {"page_object_key": "p2_lastname", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "p2_fullname", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_firstname", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "p1_firstname", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "p1_fullname", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}]}, {"page_object_key": "p2_firstname", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "p2_fullname", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}]}, {"page_object_key": "fullname", "priority_mappings": [{"document_category_key": "DEBT_COLLECTION_INFORMATION", "priority": 50}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 130}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 140}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 150}, {"document_category_key": "PASSPORT_CH", "priority": 160}, {"document_category_key": "ID", "priority": 170}, {"document_category_key": "RESIDENCE_PERMIT", "priority": 180}, {"document_category_key": "PAYSLIP", "priority": 190}]}, {"page_object_key": "addressline_fullname", "priority_mappings": [{"document_category_key": "DEBT_COLLECTION_INFORMATION", "priority": 60}]}, {"page_object_key": "firstname", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 30}, {"document_category_key": "DEBT_COLLECTION_INFORMATION", "priority": 40}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 70}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 80}, {"document_category_key": "PASSPORT_CH", "priority": 90}, {"document_category_key": "ID", "priority": 100}, {"document_category_key": "RESIDENCE_PERMIT", "priority": 110}, {"document_category_key": "PAYSLIP", "priority": 120}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_street_with_number", "field_definition_flavour": null, "field_definition_return_type": "street", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "street", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 30}, {"document_category_key": "PAYSLIP", "priority": 50}, {"document_category_key": "BANK_DOCUMENT", "priority": 70}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 90}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 110}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 130}]}, {"page_object_key": "address_block", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 40}, {"document_category_key": "PAYSLIP", "priority": 60}, {"document_category_key": "BANK_DOCUMENT", "priority": 80}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 100}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 120}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 140}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_zip", "field_definition_flavour": null, "field_definition_return_type": "zip_code", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "zip", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 30}, {"document_category_key": "PAYSLIP", "priority": 50}, {"document_category_key": "BANK_DOCUMENT", "priority": 70}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 90}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 110}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 130}]}, {"page_object_key": "address_block", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 40}, {"document_category_key": "PAYSLIP", "priority": 60}, {"document_category_key": "BANK_DOCUMENT", "priority": 80}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 100}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 120}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 140}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_city", "field_definition_flavour": null, "field_definition_return_type": "city", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "city", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 30}, {"document_category_key": "PAYSLIP", "priority": 50}, {"document_category_key": "BANK_DOCUMENT", "priority": 70}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 90}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 110}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 130}]}, {"page_object_key": "address_block", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 40}, {"document_category_key": "PAYSLIP", "priority": 60}, {"document_category_key": "BANK_DOCUMENT", "priority": 80}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 100}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 120}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 140}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_date_of_birth", "field_definition_flavour": null, "field_definition_return_type": "date", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "p1_date_of_birth", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "p2_date_of_birth", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "date_of_birth", "priority_mappings": [{"document_category_key": "PASSPORT_CH", "priority": 20}, {"document_category_key": "ID", "priority": 30}, {"document_category_key": "RESIDENCE_PERMIT", "priority": 40}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 50}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 60}, {"document_category_key": "PAYSLIP", "priority": 70}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_ahv_n13", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "ahv_new", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 20}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 30}, {"document_category_key": "PAYSLIP", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_nationality", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "nationality", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}, {"document_category_key": "PASSPORT_CH", "priority": 20}, {"document_category_key": "ID", "priority": 30}, {"document_category_key": "RESIDENCE_PERMIT", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_sex", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "salutation", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 60}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 80}, {"document_category_key": "PAYSLIP", "priority": 100}, {"document_category_key": "BANK_DOCUMENT", "priority": 110}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 120}]}, {"page_object_key": "sex", "priority_mappings": [{"document_category_key": "PASSPORT_CH", "priority": 20}, {"document_category_key": "ID", "priority": 30}, {"document_category_key": "RESIDENCE_PERMIT", "priority": 40}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 50}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 70}, {"document_category_key": "PAYSLIP", "priority": 90}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_marital_status", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "p1_marital_status", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "p2_marital_status", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "marital_status", "priority_mappings": [{"document_category_key": "PENSION_CERTIFICATE", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_employer", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "p2_employer", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 30}]}, {"page_object_key": "p1_employer", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 30}]}, {"page_object_key": "employer", "priority_mappings": [{"document_category_key": "PAYSLIP", "priority": 10}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_degree_employment", "field_definition_flavour": null, "field_definition_return_type": "integer", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "degree_employment", "priority_mappings": [{"document_category_key": "PAYSLIP", "priority": 10}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_profession", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "p2_profession", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "p1_profession", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_debt_enforcement_record_status", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "confirmation_empty", "priority_mappings": [{"document_category_key": "DEBT_COLLECTION_INFORMATION", "priority": 10}]}, {"page_object_key": "confirmation_not_empty", "priority_mappings": [{"document_category_key": "DEBT_COLLECTION_INFORMATION", "priority": 20}]}, {"page_object_key": "status", "priority_mappings": [{"document_category_key": "DEBT_COLLECTION_INFORMATION", "priority": 30}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_income_gross", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "salary_gross", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}]}, {"page_object_key": "income_gross_total", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 50}]}, {"page_object_key": "p1_income_gross_total", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 60}]}, {"page_object_key": "applicable_annual_salary_declared", "priority_mappings": []}, {"page_object_key": "salary_month_gross", "priority_mappings": [{"document_category_key": "PAYSLIP", "priority": 20}]}, {"page_object_key": "p2_income_gross_total", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 60}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "PENSION_PAYMENT_AHV", "priority": 30}, {"document_category_key": "PENSION_PAYMENT_BVG", "priority": 40}, {"document_category_key": "TAX_DECLARATION", "priority": 70}]}]}, {"field_definition_key": "hdf_person_income_net", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "income_net_total", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}]}, {"page_object_key": "salary_month_net", "priority_mappings": [{"document_category_key": "PAYSLIP", "priority": 40}]}, {"page_object_key": "p2_income_net_total", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 30}]}, {"page_object_key": "p1_income_net_total", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 30}]}, {"page_object_key": "salary_net", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_income_alimony_total", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "p2_income_alimony_total", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 50}]}, {"page_object_key": "income_alimony_children", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "income_alimony_partner", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}]}, {"page_object_key": "p1_income_alimony_partner", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 40}]}, {"page_object_key": "p2_income_alimony_partner", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 40}]}, {"page_object_key": "p1_income_alimony_children", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 30}]}, {"page_object_key": "p2_income_alimony_children", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 30}]}, {"page_object_key": "p1_income_alimony_total", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 50}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 60}, {"document_category_key": "DIVORCE_CONVENTION", "priority": 70}]}]}, {"field_definition_key": "hdf_person_expense_alimony_total", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "expense_alimony_children", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}, {"page_object_key": "expense_alimony_partner", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 30}, {"document_category_key": "DIVORCE_CONVENTION", "priority": 40}]}]}, {"field_definition_key": "hdf_person_pension2_assets_current", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "current_assets", "priority_mappings": [{"document_category_key": "PENSION_CERTIFICATE", "priority": 10}]}, {"page_object_key": "total_amount", "priority_mappings": [{"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_pension2_assets_projected_retirement", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "projected_assets_retirement", "priority_mappings": [{"document_category_key": "PENSION_CERTIFICATE", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_pension2_wef_pledging_possible_amount", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "wef_pledging_possible_amount", "priority_mappings": [{"document_category_key": "PENSION_CERTIFICATE", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_pension2_wef_pledging_registered_status", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "wef_pledging_registered_status", "priority_mappings": [{"document_category_key": "PENSION_CERTIFICATE", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_pension2_wef_withdrawal_registered_payout_amount", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "wef_withdrawal_registered_payout_amount", "priority_mappings": [{"document_category_key": "PENSION_CERTIFICATE", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_pension2_wef_withdrawal_registered_repaid_amount", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "wef_withdrawal_registered_repaid_amount", "priority_mappings": [{"document_category_key": "PENSION_CERTIFICATE", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_land_register_municipality", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "land_register_municipality", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_description", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "prop_property", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}, {"page_object_key": "property_desc", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 30}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_street", "field_definition_flavour": null, "field_definition_return_type": "street", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_property_street_no", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_address_street", "priority_mappings": [{"document_category_key": "PROPERTY_INSURANCE", "priority": 30}, {"document_category_key": "PROPERTY_VALUATION", "priority": 70}]}, {"page_object_key": "property_address", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}, {"document_category_key": "PROPERTY_INSURANCE", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 60}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_street_no", "field_definition_flavour": null, "field_definition_return_type": "street", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_property_street", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_address", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}, {"document_category_key": "PROPERTY_INSURANCE", "priority": 20}, {"document_category_key": "CONTRACT_OF_SALE", "priority": 40}, {"document_category_key": "PROPERTY_VALUATION", "priority": 60}]}, {"page_object_key": "property_address_street", "priority_mappings": [{"document_category_key": "PROPERTY_INSURANCE", "priority": 30}, {"document_category_key": "PROPERTY_VALUATION", "priority": 70}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_zip", "field_definition_flavour": null, "field_definition_return_type": "zip_code", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_address", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}, {"document_category_key": "PROPERTY_INSURANCE", "priority": 20}, {"document_category_key": "CONTRACT_OF_SALE", "priority": 40}, {"document_category_key": "PROPERTY_VALUATION", "priority": 60}]}, {"page_object_key": "property_address_zip", "priority_mappings": [{"document_category_key": "PROPERTY_INSURANCE", "priority": 30}, {"document_category_key": "PROPERTY_VALUATION", "priority": 70}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_city", "field_definition_flavour": null, "field_definition_return_type": "city", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_address", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}, {"document_category_key": "PROPERTY_INSURANCE", "priority": 20}, {"document_category_key": "CONTRACT_OF_SALE", "priority": 40}, {"document_category_key": "PROPERTY_VALUATION", "priority": 60}]}, {"page_object_key": "property_address_city", "priority_mappings": [{"document_category_key": "PROPERTY_INSURANCE", "priority": 30}, {"document_category_key": "PROPERTY_VALUATION", "priority": 70}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_value_ratio", "field_definition_flavour": null, "field_definition_return_type": "value_ratio", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_value_ratio", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}, {"document_category_key": "PROPERTY_VALUATION_GOV", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_year_construction", "field_definition_flavour": null, "field_definition_return_type": "integer", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "year_construction", "priority_mappings": [{"document_category_key": "PROPERTY_INSURANCE", "priority": 10}, {"document_category_key": "PROPERTY_VALUATION_GOV", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_area_land", "field_definition_flavour": null, "field_definition_return_type": "area", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "land_register_area", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}, {"page_object_key": "plot_size", "priority_mappings": [{"document_category_key": "PROPERTY_INSURANCE", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_pledge", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": false, "relevant_page_objects": [{"page_object_key": "prop_pledge", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_obligations", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": false, "relevant_page_objects": [{"page_object_key": "prop_obligations", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_servitudes", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": false, "relevant_page_objects": [{"page_object_key": "prop_servitudes", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_remarks", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": false, "relevant_page_objects": [{"page_object_key": "prop_remarks", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_notes", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": false, "relevant_page_objects": [{"page_object_key": "prop_notes", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_price", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_estimation_value", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_estimation_date", "field_definition_flavour": null, "field_definition_return_type": "date", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_estimation_date", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_mortgage_type", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "mo_mortgage_type", "priority_mappings": [{"document_category_key": "MORTGAGE_CONTRACT", "priority": 10}, {"document_category_key": "MORTGAGE_PRODUCT_CONFIRMATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_mortgage_interest", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "mo_interest_rate", "priority_mappings": [{"document_category_key": "MORTGAGE_CONTRACT", "priority": 10}, {"document_category_key": "MORTGAGE_PRODUCT_CONFIRMATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_mortgage_amount_total", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "mo_total_amount", "priority_mappings": [{"document_category_key": "MORTGAGE_CONTRACT", "priority": 10}, {"document_category_key": "MORTGAGE_PRODUCT_CONFIRMATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_area_floor_gross", "field_definition_flavour": null, "field_definition_return_type": "area", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_area_building_gross", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION_GOV", "priority": 30}]}, {"page_object_key": "property_area_living_gross", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 10}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_income_bonus", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "salary_irregular_benefits", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}, {"document_category_key": "PAYSLIP", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 30}, {"document_category_key": "PAYSLIP", "priority": 40}]}]}, {"field_definition_key": "hdf_person_income_additional_regular", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "salary_expenses_overall_representation", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}, {"document_category_key": "PAYSLIP", "priority": 20}]}, {"page_object_key": "salary_expenses_overall_other", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}, {"document_category_key": "PAYSLIP", "priority": 20}]}, {"page_object_key": "salary_expenses_overall_car", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}, {"document_category_key": "PAYSLIP", "priority": 20}]}, {"page_object_key": "p2_income_employed_benefits", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}, {"document_category_key": "PAYSLIP", "priority": 20}]}, {"page_object_key": "p1_income_employed_benefits", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 10}, {"document_category_key": "PAYSLIP", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "SALARY_CERTIFICATE", "priority": 30}, {"document_category_key": "PAYSLIP", "priority": 40}]}]}, {"field_definition_key": "hdf_person_income_dividends", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "income_portfolio", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}]}]}, {"field_definition_key": "hdf_person_expense_leasing", "field_definition_flavour": null, "field_definition_return_type": "currency", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "interest_paid_on_debt", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "LEASING_AGREEMENT", "priority": 20}, {"document_category_key": "CRIF_DATA_INFO", "priority": 30}, {"document_category_key": "CRIF_QUICK_CONSUMER_CHECK", "priority": 30}, {"document_category_key": "ZEK_INFO", "priority": 40}, {"document_category_key": "ZEK_CHECK", "priority": 40}]}]}, {"field_definition_key": "hdf_property_ownership", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "ownership_type", "priority_mappings": [{"document_category_key": "EXTRACT_FROM_LAND_REGISTER", "priority": 10}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_renovation", "field_definition_flavour": null, "field_definition_return_type": "integer", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": false, "relevant_page_objects": [], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}]}, {"field_definition_key": "hdf_property_parking_underground", "field_definition_flavour": null, "field_definition_return_type": "integer", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}]}, {"field_definition_key": "hdf_property_parking_garage", "field_definition_flavour": null, "field_definition_return_type": "integer", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}]}, {"field_definition_key": "hdf_property_parking_outdoor", "field_definition_flavour": null, "field_definition_return_type": "integer", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}]}, {"field_definition_key": "hdf_property_wcs_no", "field_definition_flavour": null, "field_definition_return_type": "integer", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "num_rooms_toilet", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 10}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}]}, {"field_definition_key": "hdf_property_bathrooms_no", "field_definition_flavour": null, "field_definition_return_type": "integer", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "num_rooms_bathroom", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 10}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 30}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 40}]}]}, {"field_definition_key": "hdf_property_rooms_no", "field_definition_flavour": null, "field_definition_return_type": "float", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "num_rooms_living", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 10}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_area_floor_net", "field_definition_flavour": null, "field_definition_return_type": "area", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "property_area_living_net", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 10}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_area_floor_other", "field_definition_flavour": null, "field_definition_return_type": "area", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_heat_distribution", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "heat_distribution", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 10}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_property_heat_production", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "heating_system", "priority_mappings": [{"document_category_key": "PROPERTY_VALUATION", "priority": 10}, {"document_category_key": "SALES_DOCUMENTATION", "priority": 20}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_street", "field_definition_flavour": null, "field_definition_return_type": "street", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_street_no", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "street", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 30}, {"document_category_key": "PAYSLIP", "priority": 50}, {"document_category_key": "BANK_DOCUMENT", "priority": 70}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 90}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 110}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 130}]}, {"page_object_key": "address_block", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 40}, {"document_category_key": "PAYSLIP", "priority": 60}, {"document_category_key": "BANK_DOCUMENT", "priority": 80}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 100}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 120}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 140}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_person_street_no", "field_definition_flavour": null, "field_definition_return_type": "street", "field_definition_context_strategy": null, "relevant_field_contexts": [{"relevant_field_definition_key": "hdf_person_street", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_firstname", "relevant_field_definition_flavour": null, "weight": 1.0}, {"relevant_field_definition_key": "hdf_person_lastname", "relevant_field_definition_flavour": null, "weight": 1.0}], "is_editable_hint": true, "relevant_page_objects": [{"page_object_key": "street", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 10}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 30}, {"document_category_key": "PAYSLIP", "priority": 50}, {"document_category_key": "BANK_DOCUMENT", "priority": 70}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 90}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 110}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 130}]}, {"page_object_key": "address_block", "priority_mappings": [{"document_category_key": "TAX_DECLARATION", "priority": 20}, {"document_category_key": "SALARY_CERTIFICATE", "priority": 40}, {"document_category_key": "PAYSLIP", "priority": 60}, {"document_category_key": "BANK_DOCUMENT", "priority": 80}, {"document_category_key": "BANK_STATEMENT_OF_INTEREST_CAPITAL", "priority": 100}, {"document_category_key": "PENSION_CERTIFICATE", "priority": 120}, {"document_category_key": "VESTED_BENEFITS_ACCOUNT", "priority": 140}]}], "relevant_semantic_pages": [], "relevant_semantic_documents": []}, {"field_definition_key": "hdf_minergie", "field_definition_flavour": null, "field_definition_return_type": "string", "field_definition_context_strategy": null, "relevant_field_contexts": [], "is_editable_hint": true, "relevant_page_objects": [], "relevant_semantic_pages": [{"relevant_object_type": "SEMANTIC_PAGE", "priority_mappings": [{"document_category_key": "MINERGIE_CERTIFICATE", "priority": 10}]}], "relevant_semantic_documents": [{"relevant_object_type": "SEMANTIC_DOCUMENT", "priority_mappings": [{"document_category_key": "SALES_DOCUMENTATION", "priority": 20}, {"document_category_key": "PROPERTY_VALUATION", "priority": 30}]}]}]}]