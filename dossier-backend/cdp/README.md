# Contextual Data Provider 

---
## External Data Dependencies

---
### Document Category Keys:
Currently, the document categories imported in the cdp, use the same JSON file as the dms backend. The file is located at
`dossier-backend/assets/document_category/default`

We are working with the default document categories (no specific doc cats / mapping for an account are considered)

To import the document categories, the following command is used:

```bash
python manage.py import_cdp_document_categories --file <<PATH_TO_JSON_FILE>>
```

_OR_ 

If no `--file` argument is explicitly provided, the latest file in the dossier-backend/cdp/management/assets/document_categories folder will be used.",
```bash
python manage.py import_cdp_document_categories
```


---
### Page Object Keys:

`Hyextract` project is the source for the defined/generated page object keys.
As part of the processing, the newly defined `page_object_keys` detected by `hyextract` are written to the DB and available for downstream tasks. 
Thus, we export a JSON file with all the `page_object_keys` from `hyextract` and import them into the CDP.

To import the page object keys, the following command is used:

```bash
python manage.py import_cdp_page_object_keys --file <<PATH_TO_JSON_FILE>>
```

_OR_

If no `--file` argument is explicitly provided, the latest file in the `dossier-backend/cdp/management/assets/page_object_keys` folder will be used.",
```bash
python manage.py import_cdp_page_object_keys
```


To get the latest page object keys from the `hyextract` project, checkout `hyextract` from gitlab. 

Navigate to the `hyextract` project and run the following command to export the page object keys to a JSON file:

```bash
python doc/internal/cdp/scripts/export_page_object_keys_for_cdp.py
```

This will generate a JSON file with the page object keys in the `hyextract` project at `doc/internal/cdp`
This file can be copied over to the `dossier-backend/cdp/management/assets/page_object_keys` folder and imported into the CDP.
---
### Field Set Data 

#### Importing Field Set Data

The field set data is a JSON file export of all the CDP config data for each field set configured on the DMS.

To import the field set data, the following command is used:

```bash
python manage.py import_all_field_sets --file <<PATH_TO_JSON_FILE>>  --delete-dangling-field-definitions/--no-delete-dangling-field-definitions ----delete-dangling-priority-mappings/--no-delete-dangling-priority-mappings
```

_OR_

If no `--file` argument is explicitly provided, the command will:
1. First try to use `field_set_data.json` (the working file for git tracking)
2. Fall back to the latest timestamped file in the `dossier-backend/cdp/management/assets/field_set_data` folder

We have 2 additional argument flags : 
- `--delete-dangling-field-definitions`
- `--delete-dangling-priority-mappings`.
If these flags are set, the field definitions and priority mappings present in the DB but not present in the imported JSON file will be deleted.
The default value of the `--delete-dangling-field-definitions` and `--delete-dangling-priority-mappings` arguments is `False`.

```bash
python manage.py import_all_field_sets
```

#### Exporting Field Set Data

Until we have a workflow for centrally configuring the CDP, the current workflow for a developer is to make the changes on the local dms, test it out, and export the field set data to a JSON file.
Then the command to import the field set data can be run on the different instances of the DMS (e.g. staging / demo / client test/ client prod)

To export the field set data, the following command is used:

```bash
python manage.py export_all_field_sets
```

This command creates two files in the `dossier-backend/cdp/management/assets/field_set_data` folder:
1. **`field_set_data.json`** - Working file for git tracking (no timestamp)
2. **`field_set_export_YYYYMMDD.json`** - Timestamped file for version history

The working file (`field_set_data.json`) allows easy git diff tracking between versions, while the timestamped files provide a historical record independent of git commits.

---

### Putting it all together : Initializing the CDP 

The following command can be used to import all the data (document categories, page object keys, field set data) in one go:

```bash
python manage.py cdp_init --truncate/--no-truncate
```

The `--truncate` flag will delete all the existing data in the DB before importing the new data.
This is useful when you want to start fresh with the data import, and to not have any old data in the DB.
The default value of the `--truncate` argument is `False`.

```bash
python manage.py cdp_init
```

---
Currently, the `python manage.py reset-db` command also initializes the CDP. 
Along with the other reset-db tasks, it also runs the command in  `dossier/management/commands/configure_default_field_set.py`
which imports the document categories, page object keys, and field set data, configures the `hd_internal` field set for the default account, and enables the form tab for the default account.

